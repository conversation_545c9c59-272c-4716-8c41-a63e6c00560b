package me.zhengjie.modules.system.rest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zthzinfo.common.ResponseMapBuilder;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.domain.SysUserPlate;
import me.zhengjie.modules.business.service.SysExternalAccountService;
import me.zhengjie.modules.business.service.SysProcessService;
import me.zhengjie.modules.business.service.SysUserPlateService;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.business.service.bean.RoleByComId;
import me.zhengjie.modules.business.service.bean.UserCompany;
import me.zhengjie.modules.system.domain.Role;
import me.zhengjie.modules.system.domain.bean.BankInfoBean;
import me.zhengjie.modules.system.domain.vo.ProcessInstanceVo;
import me.zhengjie.modules.system.service.RoleService;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.workflow.common.PaymentConfig;
import me.zhengjie.modules.workflow.enums.NodeTypeEnum;
import me.zhengjie.modules.workflow.enums.ProcessStatusEnum;
import me.zhengjie.modules.workflow.model.document.DProcessInstance;
import me.zhengjie.modules.workflow.model.entity.ProcessApproveLog;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import me.zhengjie.modules.workflow.service.ProcessApproveLogService;
import me.zhengjie.modules.workflow.service.ProcessInstanceService;
import me.zhengjie.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Api(tags = "系统：业务费用付款待办")
@RequestMapping("/api/businessPayment")
public class BusinessPaymentController {

    @Autowired
    SysProcessService sysProcessService;
    @Autowired
    ProcessInstanceService processInstanceService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    ProcessApproveLogService processApproveLogService;
    @Autowired
    RoleService roleService;

    @Autowired
    SysUserPlateService sysUserPlateService;
    @Autowired
    WxDepartmentService wxDepartmentService;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    SysExternalAccountService sysExternalAccountService;
    @Autowired
    PaymentConfig paymentConfig;

    private static final String COMPANY_KE="company"; // 公司key
    private static final String DEPART_KEY="Department"; // 部门key

    private Map<String, JSONObject> processIdAndCompany(List<String> pids){
        List<DProcessInstance> formValues = mongoTemplate.find(new Query(Criteria.where("_id").in(pids)), DProcessInstance.class);
        Map<String,JSONObject> map=new HashMap<>();
        if(formValues==null || formValues.size()==0){
            return map;
        }
        Set<String> companyIds=new HashSet<>();
        List<JSONObject> allInstanceList =new ArrayList<>();
        for(DProcessInstance pi:formValues){
            if (Objects.equals(pi.getFlowCode(), "paymentApprove") || Objects.equals(pi.getFlowCode(), "shipDingjinPaymentApprove")) {
                continue;
            }
            if(pi==null || pi.getFromvalue()==null || pi.getFromvalue().size()==0){
                continue;
            }
            JSONObject jo= pi.getFromvalue();
            if(jo.containsKey(COMPANY_KE)|| jo.containsKey(DEPART_KEY)){
                JSONObject j=new JSONObject();
                j.set(COMPANY_KE,jo.getStr(COMPANY_KE,""));
                j.set(DEPART_KEY,jo.getStr(DEPART_KEY,""));
                j.set("id",pi.getId());
                allInstanceList.add(j);
//                map.put(pi.getId(), j);
                if(StrUtil.isNotBlank(jo.getStr(COMPANY_KE,""))){
                    companyIds.add(jo.getStr(COMPANY_KE));
                }
                if(StrUtil.isNotBlank(jo.getStr(DEPART_KEY,""))){
                    companyIds.add(jo.getStr(DEPART_KEY));
                }
            }
        }
        formValues.clear();
        // 查询所有公司/部门
        if (companyIds.size() <= 0) {
            return map;
        }
        LambdaQueryWrapper<WxDepartment> lambdaQueryWrapper=new LambdaQueryWrapper();
        lambdaQueryWrapper.in(WxDepartment::getId,companyIds);
        lambdaQueryWrapper.select(WxDepartment::getId,WxDepartment::getName);
        List<WxDepartment> list= wxDepartmentService.list(lambdaQueryWrapper);
        Map<String,List<WxDepartment>>  mapCompany= list.stream().collect(Collectors.groupingBy(WxDepartment::getId));
        for(JSONObject jo:allInstanceList){
            if(StrUtil.isNotBlank(jo.getStr(COMPANY_KE,"")) && mapCompany.containsKey(jo.getStr(COMPANY_KE))){
                    jo.set(COMPANY_KE,mapCompany.get(jo.getStr(COMPANY_KE)).get(0).getName());
            }
            if(StrUtil.isNotBlank(jo.getStr(DEPART_KEY,"")) && mapCompany.containsKey(jo.getStr(DEPART_KEY))){
                jo.set(DEPART_KEY,mapCompany.get(jo.getStr(DEPART_KEY)).get(0).getName());
            }
            map.put(jo.getStr("id"),jo);
        }

        return map;
    }
    @RequestMapping("/getPaymentProcessListCg")
    public Map<String,Object> getPaymentProcessListCg(@RequestParam(value = "code", required = false) String code,
                                                       @RequestParam(value = "userName", required = false) String userName,
                                                       @RequestParam(value = "companyId", required = false) String companyId,
                                                       @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
                                                       @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                       @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                       @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                       @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                       @RequestParam(value = "money", required = false) String money,
                                                       @RequestParam(value = "accid", required = false) String accid){
        // 默认显示成功网联  select cp.`wx_department_id`,cp.`dict_id`,wd.`name` from `sys_company_plate` cp
        //left join `wx_department` wd on cp.`wx_department_id`=wd.`dept_id`
        // dict_id 22
        List<Long> ids = Arrays.asList(22l);
        // 排除流程名
        List<String> paymentCode = paymentConfig.getPaymentCode();
        // 排除流程名
        paymentCode.remove("paymentApprove");

        com.github.pagehelper.Page<ProcessInstance> page = processInstanceService.getPaymentProcessListNew(pageNo,pageSize,null,code,userName,null,null,ids,paymentCode,startTime,endTime,companyId, null,money);

        return getPaymentProcessListByDictId(page);

    }
    private Map<String,Object> getPaymentProcessListByDictId(com.github.pagehelper.Page<ProcessInstance> page){

//        List<String> paymentCode = PaymentConfig.PAYMENTCODE;


        List<ProcessInstance> personalTask = page.getResult();

        List<String> userIds = personalTask.stream().map(ProcessInstance::getSponsorId).distinct().collect(Collectors.toList());
        if(userIds == null || userIds.size()<=0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",page)
                    .getResult();
        }
//        List<UserCompany> userCompanies = wxDepartmentService.getuserxinxiNoMainByList(userIds);
        // 主部门
        List<UserCompany> userCompanies = wxDepartmentService.getuserxinxiByList(userIds);
        Map<String, List<UserCompany>> userCompanyMap = userCompanies.stream().collect(Collectors.groupingBy(UserCompany::getUserId));

        List<String> insIds = personalTask.stream().map(ProcessInstance::getId).collect(Collectors.toList());
        if(insIds == null || insIds.size()<=0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",page)
                    .getResult();
        }
        // 查询所有公司/部门数据
        Map<String,JSONObject> companyInstance = processIdAndCompany(insIds);
        // 返回流程id 公司/部门
        LambdaQueryWrapper<ProcessApproveLog> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.in(ProcessApproveLog::getProcessId,insIds);
        queryWrapper1.eq(ProcessApproveLog::getApproveResult,ProcessStatusEnum.CANCEL.getKey());
        List<ProcessApproveLog> logs = processApproveLogService.list(queryWrapper1);
        Map<String, List<ProcessApproveLog>> map = logs.stream().collect(Collectors.groupingBy(ProcessApproveLog::getProcessId));
        // 发起人,审批人,抄送人
        List<String> uids = personalTask.stream()
                .map(ProcessInstance::getSpId)
                .distinct()
                .collect(Collectors.toList());
        List<String> suids = personalTask.stream()
                .map(ProcessInstance::getSponsorId)
                .distinct()
                .collect(Collectors.toList());
        List<String> cuids = personalTask.stream()
                .map(ProcessInstance::getCcs)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .map(item -> new ArrayList<String>(Arrays.asList(StringUtils.delimitedListToStringArray(item, ","))))
                .reduce((x, y) -> {
                    x.addAll(y);
                    return x;
                }).orElse(new ArrayList<>());
        uids.addAll(cuids);
        uids.addAll(suids);
        uids = uids.stream().distinct().collect(Collectors.toList());
        Map<String, SysUser> userCache = new HashMap<>();
        if (CollectionUtil.isNotEmpty(uids)){
            sysUserService.listByUserIds(uids)
                    .forEach(item -> userCache.put(item.getUserId(),item));
        }
        Map<String, BankInfoBean> bankMap = new HashMap<>();
        // 查询收款银行信息 mongo
//        insIds
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(insIds));
        // 只显示 fromvalue
        List<DProcessInstance> processMos = mongoTemplate.find(query, DProcessInstance.class);
        // 查询收款银行信息
        if(CollectionUtil.isNotEmpty(processMos)){
            processMos.forEach(item -> {
                bankMap.put(item.getId(),BankInfoBean.buildBank(item,sysExternalAccountService));
            });
        }
        processMos.clear();

        List<String> roleCodes = personalTask.stream().map(ProcessInstance::getSpRoleId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<Role> rolesByCodes = roleService.getRolesByCodes(roleCodes);

        List<ProcessInstanceVo> list = personalTask.stream()
                .map(item -> {

                    SysUser sponsoruc = Optional.ofNullable(userCache.get(item.getSponsorId())).orElse(new SysUser());
                    SysUser spuc = Optional.ofNullable(userCache.get(item.getSpId())).orElse(new SysUser());
                    ProcessInstanceVo vo = new ProcessInstanceVo();
                    vo.setFlowName(item.getFlowName());
                    vo.setFlowCode(item.getFlowCode());
                    vo.setParam1(item.getGlobalParam1());
                    vo.setParam9(item.getGlobalParam9());
                    vo.setParam7(item.getGlobalParam7());
                    vo.setParam3(item.getGlobalParam3());
                    vo.setParam4(item.getGlobalParam4());
                    vo.setParam10(item.getGlobalParam10());
                    vo.setProcessId(item.getId());
                    vo.setSponsorTime(item.getSponsorTime());
                    vo.setFinishTime(item.getFinishTime());
                    vo.setNodeId(item.getStatusId());
                    vo.setSponsorName(sponsoruc.getNickName());
                    vo.setPaidMoney(item.getPaidMoney());
                    // 增加 内外帐 摘要 付款银行
                    vo.setSummary(item.getBriefContent());
                    if("waizhangchuna".equals(item.getSpRoleId())){
                        vo.setPayAccountTypeName("外帐");
                    }else if("chuna".equals(item.getSpRoleId())){
                        vo.setPayAccountTypeName("内帐");
                    }else{
                        vo.setPayAccountTypeName("");
                    }
                    if(bankMap.containsKey(item.getId())){
                        vo.setBankName(bankMap.get(item.getId()).getBankName());
                        vo.setBankAccountName(bankMap.get(item.getId()).getBankAccountName());
                        vo.setBankAccount(bankMap.get(item.getId()).getBankAccount());
                    }
                    // 判断是否流程中有公司部门 【sungf 2022/03/03】
                    if(companyInstance.containsKey(item.getId())){
                        JSONObject jcompany= companyInstance.get(item.getId());
                        vo.setDName(jcompany.getStr(DEPART_KEY));
                        vo.setCName(jcompany.getStr(COMPANY_KE));
                    }else{
                        List<UserCompany> userCompanyList = userCompanyMap.get(item.getSponsorId());
                        if(userCompanyList != null && userCompanyList.size()>0){
                            // 根据公司id获取所属公司，没有则取第一个【sungf 2022/02/22】
                            UserCompany userCompany = userCompanyList.get(0);
                            // 没有公司 则 显示主公司
//                        if(StrUtil.isNotBlank(item.getGlobalParam4())){
//                            for(UserCompany uc:userCompanyList){
//                                if(uc.getCFullId().equals(item.getGlobalParam4())){
//                                    userCompany=uc;
//                                    break;
//                                }
//                            }
//                        }
                            // 判断部门是否与公司一致
                            if(StrUtil.isNotBlank(userCompany.getDeFullId()) && StrUtil.isNotBlank(userCompany.getCFullId()) && !userCompany.getDeFullId().equals(userCompany.getCFullId())){
                                vo.setDName(userCompany.getDName());
                            }
                            vo.setCName(userCompany.getCName());
                        }
                    }


                    if(Objects.equals(Integer.parseInt(item.getStatus()),ProcessStatusEnum.CANCEL.getKey())){
                        List<ProcessApproveLog> logList = map.get(item.getId());
                        if(logList != null &&logList.size()>0){
                            ProcessApproveLog processApproveLog = logList.get(0);
                            vo.setReason(processApproveLog.getApproveComment());
                        }
                    }
                    if (StrUtil.isNotBlank(item.getSpRoleId())) {
                        List<String> roleNames = rolesByCodes.stream().filter(r -> Objects.equals(r.getEnname(), item.getSpRoleId())).map(Role::getName).collect(Collectors.toList());
                        if (roleNames != null && roleNames.size() > 0) {
                            vo.setOperName(roleNames.get(0));
                        }

                    } else {
                        vo.setOperName(spuc.getNickName());
                    }
                    vo.setSponsorAvatar(sponsoruc.getAvatarPath());
                    vo.setBriefContent(item.getBriefContent());
                    vo.setStatus(ProcessStatusEnum.getDescByKey(Integer.parseInt(item.getStatus())));
                    vo.setStatusId(Integer.parseInt(item.getStatus()));
                    if (ProcessStatusEnum.SPING.getKey() == Integer.parseInt(item.getStatus()) && (item.getStatusId()/10*10)==NodeTypeEnum.NODE7.getCode()) {
                        vo.setShowStatus("未付款");
                    }else if(ProcessStatusEnum.SPING.getKey() == Integer.parseInt(item.getStatus()) && (item.getStatusId()/10*10)== NodeTypeEnum.NODE8.getCode()){
                        vo.setShowStatus("已付款");
                    } else if(ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(item.getStatus())){
//                        vo.setShowStatus("已录入凭证");
                        vo.setShowStatus("已付款");
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        Page<ProcessInstanceVo> newpage = new Page<>();
        newpage.setTotal(page.getTotal());
        newpage.setCurrent(page.getPageNum());
        newpage.setSize(page.getPageSize());
        newpage.setRecords(list);
        return ResponseMapBuilder.newBuilder()
                .put("data",newpage)
                .putSuccess()
                .getResult();
    }
    @RequestMapping("/getPaymentProcessList")
    public Map<String,Object> getPaymentProcessList(
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "companyId", required = false) String companyId,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam(value = "money", required = false) String money,
            @RequestParam(value = "accid", required = false) String accid
    ){
        String userId = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<SysUserPlate> q1 = new LambdaQueryWrapper<>();
        q1.eq(SysUserPlate::getUserId,userId);
        List<SysUserPlate> plateList = sysUserPlateService.list(q1);
        List<Long> ids = plateList.stream().map(SysUserPlate::getDictId).collect(Collectors.toList());
        if(ids == null || ids.size()<=0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",new Page<>())
                    .getResult();
        }
//        List<String> roles = userService.getRoleCodesByUserId(SecurityUtils.getCurrentUserId());
        List<RoleByComId> rolelistFull = sysProcessService.getmyrole(SecurityUtils.getCurrentUserId());
        List<String> rolelist = rolelistFull.stream()
            .filter(roleByComId -> StrUtil.isBlank(roleByComId.getComId()))
            .map(RoleByComId::getRoleCode)
            .collect(Collectors.toList());
        List<String> rolelistByComId = rolelistFull.stream()
            .filter(roleByComId -> StrUtil.isNotBlank(roleByComId.getComId()))
            .map(roleByComId -> roleByComId.getRoleCode() + roleByComId.getComId())
            .collect(Collectors.toList());
//        List<String> paymentCode = PaymentConfig.PAYMENTCODE;
        List<String> paymentCode = paymentConfig.getPaymentCode();
        com.github.pagehelper.Page<ProcessInstance> page = processInstanceService.getPaymentProcessListNew(pageNo,pageSize,applyStatus,code,userName,userId,rolelist,ids,paymentCode,startTime,endTime,companyId, rolelistByComId,money);

        List<ProcessInstance> personalTask = page.getResult();
    
        List<String> userIds = personalTask.stream().map(ProcessInstance::getSponsorId).distinct().collect(Collectors.toList());
        if(userIds == null || userIds.size()<=0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",page)
                    .getResult();
        }
//        List<UserCompany> userCompanies = wxDepartmentService.getuserxinxiNoMainByList(userIds);
        // 主部门
        List<UserCompany> userCompanies = wxDepartmentService.getuserxinxiByList(userIds);
        Map<String, List<UserCompany>> userCompanyMap = userCompanies.stream().collect(Collectors.groupingBy(UserCompany::getUserId));
    
        List<String> insIds = personalTask.stream().map(ProcessInstance::getId).collect(Collectors.toList());
        if(insIds == null || insIds.size()<=0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",page)
                    .getResult();
        }
        // 查询所有公司/部门数据
        Map<String,JSONObject> companyInstance = processIdAndCompany(insIds);
        // 返回流程id 公司/部门
        LambdaQueryWrapper<ProcessApproveLog> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.in(ProcessApproveLog::getProcessId,insIds);
        queryWrapper1.eq(ProcessApproveLog::getApproveResult,ProcessStatusEnum.CANCEL.getKey());
        List<ProcessApproveLog> logs = processApproveLogService.list(queryWrapper1);
        Map<String, List<ProcessApproveLog>> map = logs.stream().collect(Collectors.groupingBy(ProcessApproveLog::getProcessId));
        // 发起人,审批人,抄送人
        List<String> uids = personalTask.stream()
                .map(ProcessInstance::getSpId)
                .distinct()
                .collect(Collectors.toList());
        List<String> suids = personalTask.stream()
                .map(ProcessInstance::getSponsorId)
                .distinct()
                .collect(Collectors.toList());
        List<String> cuids = personalTask.stream()
                .map(ProcessInstance::getCcs)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .map(item -> new ArrayList<String>(Arrays.asList(StringUtils.delimitedListToStringArray(item, ","))))
                .reduce((x, y) -> {
                    x.addAll(y);
                    return x;
                }).orElse(new ArrayList<>());
        uids.addAll(cuids);
        uids.addAll(suids);
        uids = uids.stream().distinct().collect(Collectors.toList());
        Map<String, SysUser> userCache = new HashMap<>();
        if (CollectionUtil.isNotEmpty(uids)){
            sysUserService.listByUserIds(uids)
                    .forEach(item -> userCache.put(item.getUserId(),item));
        }
        Map<String, BankInfoBean> bankMap = new HashMap<>();
        // 查询收款银行信息 mongo
//        insIds
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(insIds));
        // 只显示 fromvalue
        List<DProcessInstance> processMos = mongoTemplate.find(query, DProcessInstance.class);
        // 查询收款银行信息
        if(CollectionUtil.isNotEmpty(processMos)){
            processMos.forEach(item -> {
                bankMap.put(item.getId(),BankInfoBean.buildBank(item,sysExternalAccountService));
            });
        }
        processMos.clear();

        List<String> roleCodes = personalTask.stream().map(ProcessInstance::getSpRoleId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<Role> rolesByCodes = roleService.getRolesByCodes(roleCodes);

        List<ProcessInstanceVo> list = personalTask.stream()
                .map(item -> {

                    SysUser sponsoruc = Optional.ofNullable(userCache.get(item.getSponsorId())).orElse(new SysUser());
                    SysUser spuc = Optional.ofNullable(userCache.get(item.getSpId())).orElse(new SysUser());
                    ProcessInstanceVo vo = new ProcessInstanceVo();
                    vo.setFlowName(item.getFlowName());
                    vo.setFlowCode(item.getFlowCode());
                    vo.setParam1(item.getGlobalParam1());
                    vo.setParam9(item.getGlobalParam9());
                    vo.setParam7(item.getGlobalParam7());
                    vo.setParam3(item.getGlobalParam3());
                    vo.setParam4(item.getGlobalParam4());
                    vo.setParam10(item.getGlobalParam10());
                    vo.setProcessId(item.getId());
                    vo.setSponsorTime(item.getSponsorTime());
                    vo.setFinishTime(item.getFinishTime());
                    vo.setNodeId(item.getStatusId());
                    vo.setSponsorName(sponsoruc.getNickName());
                    vo.setPaidMoney(item.getPaidMoney());
                    // 增加 内外帐 摘要 付款银行
                    vo.setSummary(item.getBriefContent());
                    if("waizhangchuna".equals(item.getSpRoleId())){
                        vo.setPayAccountTypeName("外帐");
                    }else if("chuna".equals(item.getSpRoleId())){
                        vo.setPayAccountTypeName("内帐");
                    }else{
                        vo.setPayAccountTypeName("");
                    }
                    if(bankMap.containsKey(item.getId())){
                        vo.setBankName(bankMap.get(item.getId()).getBankName());
                        vo.setBankAccountName(bankMap.get(item.getId()).getBankAccountName());
                        vo.setBankAccount(bankMap.get(item.getId()).getBankAccount());
                    }
                    // 判断是否流程中有公司部门 【sungf 2022/03/03】
                    if(companyInstance.containsKey(item.getId())){
                        JSONObject jcompany= companyInstance.get(item.getId());
                        vo.setDName(jcompany.getStr(DEPART_KEY));
                        vo.setCName(jcompany.getStr(COMPANY_KE));
                    }else{
                        List<UserCompany> userCompanyList = userCompanyMap.get(item.getSponsorId());
                        if(userCompanyList != null && userCompanyList.size()>0){
                            // 根据公司id获取所属公司，没有则取第一个【sungf 2022/02/22】
                            UserCompany userCompany = userCompanyList.get(0);
                            // 没有公司 则 显示主公司
//                        if(StrUtil.isNotBlank(item.getGlobalParam4())){
//                            for(UserCompany uc:userCompanyList){
//                                if(uc.getCFullId().equals(item.getGlobalParam4())){
//                                    userCompany=uc;
//                                    break;
//                                }
//                            }
//                        }
                            // 判断部门是否与公司一致
                            if(StrUtil.isNotBlank(userCompany.getDeFullId()) && StrUtil.isNotBlank(userCompany.getCFullId()) && !userCompany.getDeFullId().equals(userCompany.getCFullId())){
                                vo.setDName(userCompany.getDName());
                            }
                            vo.setCName(userCompany.getCName());
                        }
                    }


                    if(Objects.equals(Integer.parseInt(item.getStatus()),ProcessStatusEnum.CANCEL.getKey())){
                        List<ProcessApproveLog> logList = map.get(item.getId());
                        if(logList != null &&logList.size()>0){
                            ProcessApproveLog processApproveLog = logList.get(0);
                            vo.setReason(processApproveLog.getApproveComment());
                        }
                    }
                    if (StrUtil.isNotBlank(item.getSpRoleId())) {
                        List<String> roleNames = rolesByCodes.stream().filter(r -> Objects.equals(r.getEnname(), item.getSpRoleId())).map(Role::getName).collect(Collectors.toList());
                        if (roleNames != null && roleNames.size() > 0) {
                            vo.setOperName(roleNames.get(0));
                        }

                    } else {
                        vo.setOperName(spuc.getNickName());
                    }
                    vo.setSponsorAvatar(sponsoruc.getAvatarPath());
                    vo.setBriefContent(item.getBriefContent());
                    vo.setStatus(ProcessStatusEnum.getDescByKey(Integer.parseInt(item.getStatus())));
                    vo.setStatusId(Integer.parseInt(item.getStatus()));
                    if (ProcessStatusEnum.SPING.getKey() == Integer.parseInt(item.getStatus()) && (item.getStatusId()/10*10)==NodeTypeEnum.NODE7.getCode()) {
                        vo.setShowStatus("未付款");
                    }else if(ProcessStatusEnum.SPING.getKey() == Integer.parseInt(item.getStatus()) && (item.getStatusId()/10*10)== NodeTypeEnum.NODE8.getCode()){
                        vo.setShowStatus("已付款");
                    } else if(ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(item.getStatus())){
//                        vo.setShowStatus("已录入凭证");
                        vo.setShowStatus("已付款");
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        Page<ProcessInstanceVo> newpage = new Page<>();
        newpage.setTotal(page.getTotal());
        newpage.setCurrent(page.getPageNum());
        newpage.setSize(page.getPageSize());
        newpage.setRecords(list);
        return ResponseMapBuilder.newBuilder()
                .put("data",newpage)
                .putSuccess()
                .getResult();
    }


}
